package com.unipus.digitalbook.conf.cache;

import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

@Configuration
@EnableCaching
public class CacheManagerConfig {

    public static final String CACHE_NAME_PAPER_VERSION = "paperVersion";
    // 卷题目列表
    public static final String CACHE_NAME_PAPER_QUESTION = "PaperService:questionGroup";
    // 题库题目列表
    public static final String CACHE_NAME_PAPER_BANK_QS = "ChallengePaperInstance::questionBankList";
    // 试卷详情
    public static final String CACHE_NAME_PAPER_DETAIL = "PaperService:paperDetail";
    // 试卷题目标签
    public static final String CACHE_NAME_PAPER_TAGS = "PaperService:tagProcessor";
    // 试卷题评价
    public static final String CACHE_NAME_PAPER_SCORE_TEMPLATE = "paperScoreTemplate";
    // 试卷成绩批次信息
    public static final String CACHE_NAME_PAPER_SCORE_BATCH = "paperScoreBatch";

    private static final List<String> CACHE_NAMES = List.of(
            CACHE_NAME_PAPER_VERSION,
            CACHE_NAME_PAPER_QUESTION,
            CACHE_NAME_PAPER_BANK_QS,
            CACHE_NAME_PAPER_DETAIL,
            CACHE_NAME_PAPER_TAGS,
            CACHE_NAME_PAPER_SCORE_TEMPLATE,
            CACHE_NAME_PAPER_SCORE_BATCH
    );

    // 访问后过期时间，实现自动续期
    private static final Duration ACCESS_EXPIRE_TIME = Duration.ofDays(1);
    // 写入后强制过期时间，防止数据过期
    private static final Duration FORCE_EXPIRE_TIME = Duration.ofDays(3);

    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 配置默认的Caffeine实例
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofDays(1)));

        // 注册缓存对象 - 支持访问时自动续期和统计
        CACHE_NAMES.forEach(cacheName ->
            cacheManager.registerCustomCache(cacheName,
                Caffeine.newBuilder()
                        .maximumSize(1000)
                        .expireAfterAccess(ACCESS_EXPIRE_TIME)
                        .expireAfterWrite(FORCE_EXPIRE_TIME)
                        .recordStats() // 启用统计
                        .build()));

        return cacheManager;
    }

    public <T> T getLocalCacheData(String cacheName, String key) {
        if (key == null) {
            return null;
        }
        Cache cache = cacheManager().getCache(cacheName);
        if (cache == null) {
            return null;
        }
        Cache.ValueWrapper wrapper = cache.get(key);
        if (wrapper == null) {
            return null;
        }
        @SuppressWarnings("unchecked")
        T cachedData = (T) wrapper.get();
        return cachedData;
    }

    public void addLocalCacheData(String cacheName, String key, Object value){
        if(key==null || value==null || value instanceof List<?> dataList && dataList.isEmpty() ){
            return;
        }
        Objects.requireNonNull(cacheManager().getCache(cacheName)).put(key, value);
    }

    // 格式化缓存统计信息的标题和数据格式
    private static final String[][] CACHE_STATS_FORMAT = {
        {"%-40s|","Cache Name"        ,"|%-40s" },
        {"%-10s|","Size"              ,"|%-10d" },
        {"%-12s|","HitRate(%)"        ,"|%-12.2f"},
        {"%-12s|","MissRate(%)"       ,"|%-12.2f"},
        {"%-12s|","HitCount"          ,"|%-12d" },
        {"%-12s|","MissCount"         ,"|%-12d" },
        {"%-12s|","Evictions"         ,"|%-12d" },
        {"%-12s|","LoadCount"         ,"|%-12d" },
        {"%-12s|","LoadFailures"      ,"|%-12d" },
        {"%-12s|","TotalLoadTime(ns)" ,"|%-12d" },
        {"%-15s|","Avg Load Time(ms)" ,"|%-15.2f"},
        {"%-12s|","Evict Weight"      ,"|%-12d" },
    };

    /**
     * 获取缓存统计信息
     * @return 格式化的缓存统计信息
     */
    public String getCacheStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== Caffeine Cache Statistics ===\n");
        
        // 构建标题行
        StringBuilder titleNames = new StringBuilder();
        StringBuilder dataRowFormat = new StringBuilder();
        for (String[] titleInfo : CACHE_STATS_FORMAT) {
            titleNames.append(String.format(titleInfo[0], titleInfo[1]));
            dataRowFormat.append(titleInfo[2]).append(" ");
        }
        titleNames.append("\n");
        dataRowFormat.append("\n");
        
        stats.append(titleNames);
        stats.append("-".repeat(180)).append("\n");

        for (String cacheName : CACHE_NAMES) {
            JSONObject cacheStatsObj = getCacheStatsMap(cacheName);
            CacheStats cacheStats = cacheStatsObj.getObject("stats", CacheStats.class);
            if(cacheStats==null) continue;

            stats.append(String.format(dataRowFormat.toString(),
                    cacheName,
                    cacheStatsObj.getLong("size"),
                    cacheStats.hitRate() * 100,
                    cacheStats.missRate() * 100,
                    cacheStats.hitCount(),
                    cacheStats.missCount(),
                    cacheStats.evictionCount(),
                    cacheStats.loadCount(),
                    cacheStats.loadFailureCount(),
                    cacheStats.totalLoadTime(),
                    cacheStats.averageLoadPenalty() / 1_000_000.0, // 转换为毫秒
                    cacheStats.evictionWeight()));
        }

        return stats.toString();
    }

    private JSONObject getCacheStatsMap(String cacheName) {
        JSONObject cacheStats = new JSONObject();
        CaffeineCacheManager caffeineCacheManager = (CaffeineCacheManager) cacheManager();
        Cache cache = caffeineCacheManager.getCache(cacheName);
        if (cache == null) {
            return cacheStats;
        }

        com.github.benmanes.caffeine.cache.Cache<?, ?> nativeCache =
                (com.github.benmanes.caffeine.cache.Cache<?, ?>) cache.getNativeCache();
        cacheStats.put("size", nativeCache.estimatedSize());
        cacheStats.put("stats", nativeCache.stats());
        return cacheStats;
    }

}
