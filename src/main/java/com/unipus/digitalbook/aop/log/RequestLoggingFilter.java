package com.unipus.digitalbook.aop.log;

import com.unipus.digitalbook.conf.security.WhiteListProperties;
import jakarta.annotation.Resource;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import java.io.IOException;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RequestLoggingFilter implements Filter {

    @Resource
    private WhiteListProperties whiteListProperties;

    private static final String LOG_FORMAT = "%-16s: %s%n";

    private static final int MAX_LOG_BYTES = 2000;


    // 使用AntPathMatcher进行URL模式匹配
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    private boolean shouldSkipLogging(HttpServletRequest request) {
        if (request == null) {
            return true;
        }

        List<String> excludeUrls = List.of(whiteListProperties.getLogFilterExcludeUrls());
        if(excludeUrls.isEmpty()){
            return false;
        }

        String requestUri = request.getRequestURI();
        return requestUri != null && excludeUrls.stream().anyMatch(pattern -> antPathMatcher.match(pattern, requestUri));
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        String contentType = request.getContentType();
        if (contentType != null && contentType.toLowerCase().startsWith("multipart/")) {
            log.info("Multipart request detected, skipping logging.");
            chain.doFilter(request, response);
            return;
        }
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        if(shouldSkipLogging(httpServletRequest)){
            chain.doFilter(request, response);
            return;
        }

        // Wrap request and response to allow multiple reads
        CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(httpServletRequest);
        CachedBodyHttpServletResponse wrappedResponse = new CachedBodyHttpServletResponse(httpServletResponse);
        // requestId 先获取header中的x-request-id，如果没有再使用uuid生成
        String requestId = httpServletRequest.getHeader("x-request-id");
        if (StringUtils.isBlank(requestId)) {
            requestId = java.util.UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        }

        wrappedRequest.setAttribute("startTime", System.currentTimeMillis());

        // 设置到 MDC
        MDC.put("requestId", requestId);
        wrappedRequest.setAttribute("requestId", requestId);

        long startTime = System.currentTimeMillis();

        // 判断是否跳过日志
        boolean skipLog = shouldSkipLogging(wrappedRequest);
        if (!skipLog) {
            // 1. 打印请求日志（处理前）
            logRequest(wrappedRequest, requestId);
        }

        try {
            // 2. 继续处理
            chain.doFilter(wrappedRequest, wrappedResponse);
        } finally {
            if (!skipLog) {
                long duration = System.currentTimeMillis() - startTime;
                // 3. 打印响应日志（处理后）
                logResponse(wrappedRequest, wrappedResponse, requestId, duration);
            }
            wrappedResponse.close();
            // 4. 清理 MDC，防止内存泄漏
            MDC.remove("requestId");
            MDC.clear();
        }
    }

    private void logRequest(CachedBodyHttpServletRequest request, String requestId) {
        try {
            StringBuilder logMsg = new StringBuilder();
            logMsg.append("\n=====================[ REQUEST  IN ]======================\n");
            logMsg.append(String.format(LOG_FORMAT, "RequestId", requestId));
            logMsg.append(String.format(LOG_FORMAT, "URI", request.getRequestURI()));
            logMsg.append(String.format(LOG_FORMAT, "Method", request.getMethod()));
            logMsg.append(String.format("%-16s: %n", "Headers"));
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                logMsg.append(String.format("    %-18s: %s%n", headerName, request.getHeader(headerName)));
            }
            logMsg.append(String.format("%-16s: %n", "wholeIp"));
            String[] ips = request.getHeader("X-Forwarded-For") != null ? request.getHeader("X-Forwarded-For").split(",") : new String[0];
            for (String ip : ips) {
                logMsg.append(String.format("    %-13s  %s%n", "-", ip.trim()));
            }
            logMsg.append(String.format(LOG_FORMAT, "ip", request.getRemoteAddr()));
            logMsg.append(String.format(LOG_FORMAT, "Content-Type", request.getContentType()));
            logMsg.append(String.format(LOG_FORMAT, "Query Params", getRequestParams(request)));
            logMsg.append(String.format(LOG_FORMAT, "Request Body", getRequestBody(request)));
            logMsg.append("========================================================%n");
            log.info(logMsg.toString());
        } catch (Exception e) {
            log.error("Error logging request", e);
        }
    }

    private void logResponse(CachedBodyHttpServletRequest request, CachedBodyHttpServletResponse response, String requestId, long duration) {
        try {
            StringBuilder logMsg = new StringBuilder();
            logMsg.append("\n=====================[ RESPONSE OUT ]====================\n");
            logMsg.append(String.format(LOG_FORMAT, "RequestId", requestId));
            logMsg.append(String.format(LOG_FORMAT, "URI", request.getRequestURI()));
            logMsg.append(String.format("%-16s: %d%n", "Status", response.getStatus()));
            logMsg.append(String.format(LOG_FORMAT, "Response Body", getResponseBody(response)));
            logMsg.append(String.format("%-16s: %d ms URI - %s%n", "Duration", duration, request.getRequestURI()));
            logMsg.append("========================================================%n");
            if (response.getStatus() >= 400) {
                log.warn(logMsg.toString());
            } else {
                log.info(logMsg.toString());
            }
        } catch (Exception e) {
            log.error("Error logging response", e);
        }
    }

    private String getRequestParams(HttpServletRequest request) {
        Map<String, String[]> paramMap = request.getParameterMap();
        if (paramMap.isEmpty()) return "-";
        return paramMap.entrySet().stream()
                .map(e -> e.getKey() + "=" + String.join(",", e.getValue()))
                .collect(Collectors.joining("&"));
    }

    private String getRequestBody(CachedBodyHttpServletRequest request) {
        long contentLength = request.getContentLengthLong();
        if (contentLength <= 0) {
            return "-";
        }
        return request.getContentAsString(MAX_LOG_BYTES);
    }

    private String getResponseBody(CachedBodyHttpServletResponse response) {
        int bufferSize = response.getBufferSize();
        if (bufferSize <= 0) {
            return "-";
        }
        if (bufferSize > MAX_LOG_BYTES) {
            return "[response too large]: " + bufferSize + ":" + response.peekContentAsString(100) + "......";
        }
        return response.getContentAsString();
    }
}
