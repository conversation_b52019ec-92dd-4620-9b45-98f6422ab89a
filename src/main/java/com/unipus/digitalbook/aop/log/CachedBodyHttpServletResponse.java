package com.unipus.digitalbook.aop.log;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * HttpServletResponse wrapper that caches the response body.
 */
public class CachedBodyHttpServletResponse extends HttpServletResponseWrapper {

    private final ByteArrayOutputStream content = new ByteArrayOutputStream();
    private ServletOutputStream outputStream;
    private PrintWriter writer;
    private String characterEncoding;

    public CachedBodyHttpServletResponse(HttpServletResponse response) {
        super(response);
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (writer != null) {
            throw new IllegalStateException("getWriter() has already been called on this response.");
        }

        if (outputStream == null) {
            outputStream = new TeeServletOutputStream(super.getOutputStream(), content);
        }
        return outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if (outputStream != null) {
            throw new IllegalStateException("getOutputStream() has already been called on this response.");
        }

        if (writer == null) {
            characterEncoding = getCharacterEncoding();
            if (characterEncoding == null) {
                characterEncoding = StandardCharsets.UTF_8.name();
                setCharacterEncoding(characterEncoding);
            }
            writer = new PrintWriter(new OutputStreamWriter(new TeeServletOutputStream(super.getOutputStream(), content), characterEncoding));
        }
        return writer;
    }

    public String getContentAsString() {
        if (writer != null) {
            writer.flush();
        }
        if (characterEncoding != null) {
            return content.toString(StandardCharsets.UTF_8);
        }
        return content.toString();
    }

    /**
     * 临时读取缓存数据的方法，不影响缓存数据的最终输出
     * 返回当前缓存中的数据副本
     *
     * @return 当前缓存的响应内容字符串
     */
    public String peekContentAsString() {
        // 刷新writer确保所有数据都写入到缓存中
        if (writer != null) {
            writer.flush();
        }

        // 创建缓存数据的副本进行读取，不影响原始缓存
        byte[] cachedBytes = content.toByteArray();

        if (characterEncoding != null) {
            return new String(cachedBytes, StandardCharsets.UTF_8);
        }
        return new String(cachedBytes);
    }

    /**
     * 临时读取缓存数据的方法，返回字节数组副本
     * 不影响缓存数据的最终输出
     *
     * @return 当前缓存的响应内容字节数组副本
     */
    public byte[] peekContentAsBytes() {
        // 刷新writer确保所有数据都写入到缓存中
        if (writer != null) {
            writer.flush();
        }

        // 返回缓存数据的副本，不影响原始缓存
        return content.toByteArray();
    }

    /**
     * 获取当前缓存的数据大小
     *
     * @return 缓存数据的字节数
     */
    public int getCachedContentSize() {
        return content.size();
    }

    public void close() throws IOException {
        if (writer != null) {
            writer.close();
        }
        if (outputStream != null) {
            outputStream.close();
        }
    }

    private static class TeeServletOutputStream extends ServletOutputStream {

        private final ServletOutputStream original;
        private final ByteArrayOutputStream copy;

        public TeeServletOutputStream(ServletOutputStream original, ByteArrayOutputStream copy) {
            this.original = original;
            this.copy = copy;
        }

        @Override
        public void write(int b) throws IOException {
            original.write(b);
            copy.write(b);
        }

        @Override
        public boolean isReady() {
            return original.isReady();
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {
            original.setWriteListener(writeListener);
        }
    }
}
