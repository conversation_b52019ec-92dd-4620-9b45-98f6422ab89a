package com.unipus.digitalbook.aop.log;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * HttpServletResponse wrapper that caches the response body.
 */
public class CachedBodyHttpServletResponse extends HttpServletResponseWrapper {

    private final ByteArrayOutputStream content = new ByteArrayOutputStream();
    private ServletOutputStream outputStream;
    private PrintWriter writer;
    private String characterEncoding;

    public CachedBodyHttpServletResponse(HttpServletResponse response) {
        super(response);
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (writer != null) {
            throw new IllegalStateException("getWriter() has already been called on this response.");
        }

        if (outputStream == null) {
            outputStream = new TeeServletOutputStream(super.getOutputStream(), content);
        }
        return outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if (outputStream != null) {
            throw new IllegalStateException("getOutputStream() has already been called on this response.");
        }

        if (writer == null) {
            characterEncoding = getCharacterEncoding();
            if (characterEncoding == null) {
                characterEncoding = StandardCharsets.UTF_8.name();
                setCharacterEncoding(characterEncoding);
            }
            writer = new PrintWriter(new OutputStreamWriter(new TeeServletOutputStream(super.getOutputStream(), content), characterEncoding));
        }
        return writer;
    }

    public String getContentAsString() {
        if (writer != null) {
            writer.flush();
        }
        if (characterEncoding != null) {
            return content.toString(StandardCharsets.UTF_8);
        }
        return content.toString();
    }

    /**
     * 获取当前缓存的数据大小
     *
     * @return 缓存数据的字节数
     */
    public int getCachedContentSize() {
        return content.size();
    }

    /**
     * 临时读取指定长度的缓存数据，不影响缓存数据的最终输出
     *
     * @param length 要读取的字节数
     * @return 指定长度的缓存数据字节数组副本
     * @throws IllegalArgumentException 如果长度参数无效
     */
    public byte[] peekContentAsBytes(int length) {
        if (length < 0) {
            throw new IllegalArgumentException("Length cannot be negative");
        }

        // 刷新writer确保所有数据都写入到缓存中
        if (writer != null) {
            writer.flush();
        }

        // 获取缓存数据的副本
        byte[] cachedBytes = content.toByteArray();

        // 如果请求的长度大于等于缓存大小，返回全部数据
        if (length >= cachedBytes.length) {
            return cachedBytes;
        }

        // 创建指定长度的数组并复制数据
        byte[] result = new byte[length];
        System.arraycopy(cachedBytes, 0, result, 0, length);
        return result;
    }

    /**
     * 临时读取指定长度的缓存数据并转换为字符串，不影响缓存数据的最终输出
     *
     * @param length 要读取的字节数
     * @return 指定长度的缓存数据字符串
     * @throws IllegalArgumentException 如果长度参数无效
     */
    public String peekContentAsString(int length) {
        byte[] bytes = peekContentAsBytes(length);

        if (characterEncoding != null) {
            return new String(bytes, StandardCharsets.UTF_8);
        }
        return new String(bytes);
    }

    public void close() throws IOException {
        if (writer != null) {
            writer.close();
        }
        if (outputStream != null) {
            outputStream.close();
        }
    }

    private static class TeeServletOutputStream extends ServletOutputStream {

        private final ServletOutputStream original;
        private final ByteArrayOutputStream copy;

        public TeeServletOutputStream(ServletOutputStream original, ByteArrayOutputStream copy) {
            this.original = original;
            this.copy = copy;
        }

        @Override
        public void write(int b) throws IOException {
            original.write(b);
            copy.write(b);
        }

        @Override
        public boolean isReady() {
            return original.isReady();
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {
            original.setWriteListener(writeListener);
        }
    }
}
