package com.unipus.digitalbook.aop.log;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * HttpServletResponse wrapper that caches the response body.
 */
public class CachedBodyHttpServletResponse extends HttpServletResponseWrapper {

    private final ByteArrayOutputStream content = new ByteArrayOutputStream();
    private ServletOutputStream outputStream;
    private PrintWriter writer;
    private String characterEncoding;

    public CachedBodyHttpServletResponse(HttpServletResponse response) {
        super(response);
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (writer != null) {
            throw new IllegalStateException("getWriter() has already been called on this response.");
        }

        if (outputStream == null) {
            outputStream = new TeeServletOutputStream(super.getOutputStream(), content);
        }
        return outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if (outputStream != null) {
            throw new IllegalStateException("getOutputStream() has already been called on this response.");
        }

        if (writer == null) {
            characterEncoding = getCharacterEncoding();
            if (characterEncoding == null) {
                characterEncoding = StandardCharsets.UTF_8.name();
                setCharacterEncoding(characterEncoding);
            }
            writer = new PrintWriter(new OutputStreamWriter(new TeeServletOutputStream(super.getOutputStream(), content), characterEncoding));
        }
        return writer;
    }

    public String getContentAsString() {
        if (writer != null) {
            writer.flush();
        }
        if (characterEncoding != null) {
            return content.toString(StandardCharsets.UTF_8);
        }
        return content.toString();
    }

    /**
     * 获取当前缓存的数据大小
     *
     * @return 缓存数据的字节数
     */
    public int getCachedContentSize() {
        return content.size();
    }

    /**
     * 临时读取指定长度的缓存数据，不影响缓存数据的最终输出
     * 优化版本：避免不必要的完整数据复制
     *
     * @param length 要读取的字节数
     * @return 指定长度的缓存数据字节数组副本
     * @throws IllegalArgumentException 如果长度参数无效
     */
    public byte[] peekContentAsBytes(int length) {
        if (length < 0) {
            throw new IllegalArgumentException("Length cannot be negative");
        }

        if (length == 0) {
            return new byte[0];
        }

        // 刷新writer确保所有数据都写入到缓存中
        if (writer != null) {
            writer.flush();
        }

        // 获取当前缓存大小，避免不必要的数据复制
        int currentSize = content.size();

        // 如果请求的长度大于等于缓存大小，返回全部数据
        if (length >= currentSize) {
            return content.toByteArray();
        }

        // 高效的部分数据读取：直接从ByteArrayOutputStream读取指定长度
        return peekContentAsBytesOptimized(length);
    }

    /**
     * 临时读取指定长度的缓存数据并转换为字符串，不影响缓存数据的最终输出
     *
     * @param length 要读取的字节数
     * @return 指定长度的缓存数据字符串
     * @throws IllegalArgumentException 如果长度参数无效
     */
    public String peekContentAsString(int length) {
        byte[] bytes = peekContentAsBytes(length);

        if (characterEncoding != null) {
            return new String(bytes, StandardCharsets.UTF_8);
        }
        return new String(bytes);
    }
    
    /**
     * 优化的部分数据读取方法
     * 直接从ByteArrayOutputStream的内部缓冲区读取，避免完整数据复制
     */
    private byte[] peekContentAsBytesOptimized(int length) {
        // 使用反射访问ByteArrayOutputStream的内部缓冲区（高性能方案）
        try {
            java.lang.reflect.Field bufField = ByteArrayOutputStream.class.getDeclaredField("buf");
            bufField.setAccessible(true);
            byte[] internalBuffer = (byte[]) bufField.get(content);

            // 直接从内部缓冲区复制指定长度的数据
            int actualLength = Math.min(length, content.size());
            byte[] result = new byte[actualLength];
            System.arraycopy(internalBuffer, 0, result, 0, actualLength);
            return result;

        } catch (Exception e) {
            // 反射失败时回退到标准方法
            return peekContentAsBytesFallback(length);
        }
    }

    /**
     * 回退方案：标准的部分数据读取方法
     */
    private byte[] peekContentAsBytesFallback(int length) {
        byte[] cachedBytes = content.toByteArray();
        int actualLength = Math.min(length, cachedBytes.length);
        byte[] result = new byte[actualLength];
        System.arraycopy(cachedBytes, 0, result, 0, actualLength);
        return result;
    }

    /**
     * 高性能版本：读取指定长度的缓存数据（推荐用于频繁调用场景）
     * 使用更高效的实现，避免不必要的数据复制
     *
     * @param length 要读取的字节数
     * @return 指定长度的缓存数据字节数组副本
     * @throws IllegalArgumentException 如果长度参数无效
     */
    public byte[] peekContentAsBytesEfficient(int length) {
        if (length < 0) {
            throw new IllegalArgumentException("Length cannot be negative");
        }

        if (length == 0) {
            return new byte[0];
        }

        // 刷新writer确保所有数据都写入到缓存中
        if (writer != null) {
            writer.flush();
        }

        int currentSize = content.size();

        // 如果请求长度为0或缓存为空
        if (currentSize == 0) {
            return new byte[0];
        }

        // 如果请求的长度大于等于缓存大小，返回全部数据
        if (length >= currentSize) {
            return content.toByteArray();
        }

        // 对于小数据量，直接使用标准方法
        if (currentSize <= 1024) { // 1KB以下
            return peekContentAsBytesFallback(length);
        }

        // 对于大数据量，使用优化方法
        return peekContentAsBytesOptimized(length);
    }

    public void close() throws IOException {
        if (writer != null) {
            writer.close();
        }
        if (outputStream != null) {
            outputStream.close();
        }
    }

    private static class TeeServletOutputStream extends ServletOutputStream {

        private final ServletOutputStream original;
        private final ByteArrayOutputStream copy;

        public TeeServletOutputStream(ServletOutputStream original, ByteArrayOutputStream copy) {
            this.original = original;
            this.copy = copy;
        }

        @Override
        public void write(int b) throws IOException {
            original.write(b);
            copy.write(b);
        }

        @Override
        public boolean isReady() {
            return original.isReady();
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {
            original.setWriteListener(writeListener);
        }
    }
}
